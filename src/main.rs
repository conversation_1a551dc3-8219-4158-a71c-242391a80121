#![forbid(unsafe_code)]
#![deny(clippy::all)]
#![deny(unreachable_pub)]
#![deny(clippy::correctness)]
#![deny(clippy::suspicious)]
#![deny(clippy::style)]
#![deny(clippy::complexity)]
#![deny(clippy::perf)]
#![deny(clippy::pedantic)]
#![deny(clippy::std_instead_of_core)]
#![allow(clippy::unreadable_literal)]
mod http_listener;
mod utils;

use crate::http_listener::{HttpListener, PlatformListener};
use tracing::error;
use tracing_subscriber::EnvFilter;
use tracing_subscriber::prelude::*;

fn main() -> anyhow::Result<()> {
    let env_filter = EnvFilter::try_from_default_env().unwrap_or(EnvFilter::new(
        "debug,hyper_util=warn,reqwest=warn,rustls=warn,pktmon=warn,pcap=warn",
    ));
    let fmt_layer = tracing_subscriber::fmt::layer();

    tracing_subscriber::registry()
        .with(fmt_layer)
        .with(env_filter)
        .init();

    loop {
        if let Err(e) = PlatformListener.listen() {
            error!("Error in HTTP listener: {e}");
        }
        std::thread::sleep(core::time::Duration::from_secs(10));
    }
}
