name: Rust CI/CD

on:
  push:
  pull_request:
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always

permissions:
  contents: write

jobs:
  fmt:
    name: Format
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
          rustup component add rustfmt
      - name: Rustfmt Check
        uses: actions-rust-lang/rustfmt@v1

  deps:
    name: Dependencies
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
      - name: Machete
        uses: bnjbvr/cargo-machete@7959c845782fed02ee69303126d4a12d64f1db18

  typos:
    name: Typos
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
      - uses: taiki-e/cache-cargo-install-action@v2
        with:
          tool: typos-cli
      - name: Typos Check
        run: typos src/

  lint:
    name: Lint
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: ubuntu-latest
            target: x86_64-unknown-linux-gnu
          - platform: windows-latest
            target: x86_64-pc-windows-gnu
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v5
      - name: Install Build Tools
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libpcap-dev
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "build-cache-${{ runner.os }}-${{ runner.arch }}-stable"
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
          rustup target add ${{ matrix.target }}
      - name: Cargo Clippy (Deny Warnings)
        run: cargo clippy --all-targets --locked -- -D warnings

  test:
    name: Test
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: ubuntu-latest
            target: x86_64-unknown-linux-gnu
          - platform: windows-latest
            target: x86_64-pc-windows-gnu
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v5
      - name: Install Build Tools
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libpcap-dev
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "build-cache-${{ runner.os }}-${{ runner.arch }}-stable"
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
          rustup target add ${{ matrix.target }}
      - name: Run tests
        run: cargo test --locked

  build:
    name: Build
    needs: [ fmt, deps, typos, lint, test ]
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            binary_name: deadlock-api-ingest
            asset_name: deadlock-api-ingest-ubuntu-latest
          - platform: windows-latest
            target: x86_64-pc-windows-gnu
            binary_name: deadlock-api-ingest.exe
            asset_name: deadlock-api-ingest-windows-latest.exe
    runs-on: ${{ matrix.platform }}
    outputs:
      version: ${{ steps.version.outputs.version }}
    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0  # Fetch full history for proper versioning
      - name: Install dependencies (Linux only)
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libpcap-dev
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "build-cache-${{ runner.os }}-${{ runner.arch }}-stable"
          key: "${{ matrix.target }}"
      - name: Update Rust toolchain
        run: |
          rustup override set stable
          rustup update stable
          rustup target add ${{ matrix.target }}
      - name: Generate version
        id: version
        run: |
          # Generate semantic version based on commit count and short SHA
          COMMIT_COUNT=$(git rev-list --count HEAD)
          SHORT_SHA=$(git rev-parse --short HEAD)
          VERSION="v0.1.$COMMIT_COUNT-$SHORT_SHA"
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Generated version: $VERSION"
        shell: bash
      - name: Build release binary
        run: cargo build --locked --release --target ${{ matrix.target }}
      - name: Prepare binary (Linux)
        if: matrix.platform == 'ubuntu-latest'
        run: |
          cp target/${{ matrix.target }}/release/${{ matrix.binary_name }} ${{ matrix.asset_name }}
          chmod +x ${{ matrix.asset_name }}
          ls -la ${{ matrix.asset_name }}
      - name: Prepare binary (Windows)
        if: matrix.platform == 'windows-latest'
        run: |
          copy target\${{ matrix.target }}\release\${{ matrix.binary_name }} ${{ matrix.asset_name }}
          dir ${{ matrix.asset_name }}
      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.asset_name }}
          path: ${{ matrix.asset_name }}
          retention-days: 1

  release:
    name: Create Release
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/master'
    steps:
      - uses: actions/checkout@v5
      - name: Download all artifacts
        uses: actions/download-artifact@v5
        with:
          path: ./artifacts
      - name: Prepare release assets
        run: |
          mkdir -p ./release-assets
          find ./artifacts -name "deadlock-api-ingest-*" -type f -exec cp {} ./release-assets/ \;
          ls -la ./release-assets/
      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ needs.build.outputs.version }}
          name: "Deadlock API Ingest ${{ needs.build.outputs.version }}"
          body: |
            ## Deadlock API Ingest ${{ needs.build.outputs.version }}

            Automated release with the latest changes from master branch.

            ### Installation

            **Windows (PowerShell - Run as Administrator):**
            Open a Powershell window as an administrator and run the following command:
            ```powershell
            irm https://github.com/deadlock-api/deadlock-api-ingest/releases/download/${{ needs.build.outputs.version }}/install-windows.ps1 | iex
            ```

            **Linux (Bash - Run with sudo):**
            Open a terminal and run the following command:
            ```bash
            curl -fsSL https://github.com/deadlock-api/deadlock-api-ingest/releases/download/${{ needs.build.outputs.version }}/install-linux.sh | sudo bash
            ```

            ### Changes
            - Latest updates from commit ${{ github.sha }}
          files: |
            ./release-assets/*
            ./install-linux.sh
            ./install-windows.ps1
            ./README.md
          draft: false
          prerelease: false
          make_latest: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
